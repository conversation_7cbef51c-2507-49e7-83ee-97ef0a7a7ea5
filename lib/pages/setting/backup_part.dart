// ignore_for_file: use_build_context_synchronously

import 'package:adv_yijing/core/zip_backup.dart';
import 'package:adv_yijing/hive/hivedb_yijing.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

import '../../controllers/setting_controller.dart';

class BackupPart extends StatelessWidget {
  final void Function()? onUpdated;
  const BackupPart({super.key, this.onUpdated});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      spacing: 8,
      children: [
        Expanded(
          child: buildBackupButton(),
        ),
        Expanded(
          child: buildRestoreButton(),
        ),
      ],
    );
  }

  Widget buildBackupButton() {
    final colorScheme = Get.theme.colorScheme;
    final buttonStyle = ElevatedButton.styleFrom(
      backgroundColor: colorScheme.primary,
      foregroundColor: colorScheme.onPrimary,
    );
    final setting = Get.find<SettingController>();
    return ElevatedButton(
      style: buttonStyle,
      onPressed: () async {
        try {
          final extDir = await getExternalStorageDirectory();
          final zhiFilePath = await FilePicker.platform.saveFile(initialDirectory: extDir?.path);
          if (zhiFilePath == null) return;
          ZipBackup.backup(zhiFilePath, (String tempDirPath) async {
            await setting.jsonBackup();
            final hivePath = await HiveDBYijing().backupHiveBox(tempDirPath);
            return [
              setting.backupFileName,
              hivePath,
            ];
          });
          Get.snackbar(
            "設定備份",
            "設定備份已完成！",
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: colorScheme.secondary,
            colorText: colorScheme.onSecondary,
          );
        } catch (e) {
          Get.snackbar(
            "備份失敗",
            e.toString(),
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: colorScheme.error,
            colorText: colorScheme.onError,
          );
        }
      },
      child: const Text("備份"),
    );
  }

  Widget buildRestoreButton() {
    final colorScheme = Get.theme.colorScheme;
    final buttonStyle = ElevatedButton.styleFrom(
      backgroundColor: colorScheme.secondary,
      foregroundColor: colorScheme.onSecondary,
    );
    final setting = Get.find<SettingController>();
    return ElevatedButton(
      style: buttonStyle,
      onPressed: () async {
        try {
          final extDir = await getExternalStorageDirectory();
          final zhiFilePath = await FilePicker.platform.pickFiles(initialDirectory: extDir?.path);
          if (zhiFilePath == null) return;
          ZipBackup.restore(zhiFilePath.files.first.path!, (restoredFilePath) async {
            print("restoring $restoredFilePath");
            if (restoredFilePath.endsWith(setting.backupFileName)) {
              await setting.jsonRestore();
            } else if (restoredFilePath.endsWith('${HiveDBYijing().boxName}.hive')) {
              // 將 hive 檔案從暫存目錄複製到正確位置
              final tempDir = await getTemporaryDirectory();
              await HiveDBYijing().restoreHiveBox(tempDir.path);
            }
          });
          Get.snackbar(
            "設定還原",
            "設定已使用備份還原！",
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: colorScheme.secondary,
            colorText: colorScheme.onSecondary,
          );
          onUpdated?.call();
        } catch (e) {
          // 如果自動還原失敗，提供手動選擇選項
          Get.snackbar(
            "自動還原失敗",
            "無法自動找到備份文件，請嘗試手動選擇備份文件。\n錯誤: ${e.toString()}",
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: colorScheme.error,
            colorText: colorScheme.onError,
            duration: Duration(seconds: 5),
          );
        }
      },
      child: const Text("還原"),
    );
  }
}
