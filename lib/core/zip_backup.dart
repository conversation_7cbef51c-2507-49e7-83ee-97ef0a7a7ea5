import 'dart:io';

import 'package:archive/archive_io.dart';
import 'package:path_provider/path_provider.dart';

typedef BackupFunction = Future<List<String>> Function(String tempDirPath);
typedef RestoreFunction = Future<void> Function(String restoredFilePath);

class ZipBackup {
  static String zipFileName = "adv_yijing_backup.zip";

  /// 將要備份的檔案集中在暫存目錄，打包成 zip 檔案，再複製到備份目錄
  static Future backup(String zipFilePath, BackupFunction prepareBackupFiles) async {
    final zipFileName = zipFilePath.split("/").last;
    final tempDir = await getTemporaryDirectory();
    final zipFile = File("${tempDir.path}/$zipFileName");
    final backupFilePaths = await prepareBackupFiles(tempDir.path);
    final archive = Archive();
    for (var filePath in backupFilePaths) {
      final file = File("${tempDir.path}/$filePath");
      archive.addFile(ArchiveFile(filePath, file.lengthSync(), file.readAsBytesSync()));
    }
    final zipBytes = ZipEncoder().encode(archive);
    await zipFile.writeAsBytes(zipBytes);
    await zipFile.copy(zipFilePath);
  }

  /// 將備份的 zip 檔案解壓縮到暫存目錄，再由 restoreFunction 進行還原
  static Future restore(String zipFilePath, RestoreFunction onFileRestored) async {
    final zipFile = File(zipFilePath);
    if (!zipFile.existsSync()) throw "備份檔案不存在";
    final tempDir = await getTemporaryDirectory();
    final archive = ZipDecoder().decodeBytes(await zipFile.readAsBytes());
    for (var file in archive) {
      final filePath = file.name;
      final restoredFilePath = "${tempDir.path}/$filePath";
      final restoredFile = File(restoredFilePath);
      await restoredFile.writeAsBytes(file.content);
      await onFileRestored(restoredFilePath);
    }
  }
}
