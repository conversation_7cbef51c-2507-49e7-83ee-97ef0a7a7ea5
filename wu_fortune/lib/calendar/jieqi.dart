import 'package:wu_core/wu_extensions.dart';
import 'package:wu_fortune/wu_calendar.dart';
import 'jieqi_data.dart';

class Jieqi {
  /// 節氣在資料中的索引
  final int listIndex;
  Jieqi(this.listIndex) {
    if (listIndex < 0 || listIndex >= jieqiData.length) {
      throw ArgumentError("jieqiindex out of range");
    }
  }
  factory Jieqi.byYearIndex(int year, int jieqiIndex) {
    return Jieqi(getListIndex(year, jieqiIndex));
  }
  factory Jieqi.byYearName(int year, String jieqiName) {
    final index = JIEQI_NAMES.indexOf(jieqiName);
    return Jieqi(getListIndex(year, index));
  }

  /// 找出日期歸屬的節氣
  factory Jieqi.bySolar(DateTime solar) {
    final y = solar.year;
    final m = solar.month - 1;
    final solarStr = solar.ymd();
    final first = Jieqi.byYearIndex(y, m * 2);
    final last = Jieqi.byYearIndex(y, m * 2 + 1);
    print("$solarStr, ${first.dateTime.ymd()}, ${last.dateTime.ymd()}");
    if (solarStr >= last.dateTime.ymd()) return last;
    if (solarStr >= first.dateTime.ymd()) return first;
    return Jieqi.byYearIndex(y, m * 2 - 1);
  }

  int get index => listIndex % 24;
  String get name => JIEQI_NAMES[index];
  DateTime get dateTime => DateTime.parse(jieqiData[listIndex]);

  /// 有效資料範圍
  static const (int, int) dataRange = (1800, 2100);

  /// 取得節氣在資料中的索引
  /// 因為使用查表運算，即使節氣為負數也會對應到適當的年份
  static int getListIndex(int year, int termIndex) {
    final (start, _) = dataRange;
    return (year - start) * 24 + termIndex;
  }
}
